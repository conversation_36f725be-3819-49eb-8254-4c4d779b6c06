#include "esp_pm.h"
#include "esp_sleep.h"
#include "driver/uart.h"
#include "soc/uart_pins.h"
#include "driver/gpio.h"
#include "esp_idf_version.h"
#include "sdkconfig.h"
#include "tuya_log.h"

esp_pm_lock_handle_t dfs_lock;
//TODOFIX: 当前只适合串口0，其它串口需要修改适配
void uart_wakeup_config(int uart_num)
{
#if 0
    if (uart_num == CONFIG_ESP_CONSOLE_UART_NUM) {
        /* temp fix for uart garbled output, can be removed when IDF-5683 done */
        uart_wait_tx_idle_polling(uart_num);
    }
#endif
    /* Only uart0 and uart1 (if has) support to be configured as wakeup source */
    uart_set_pin(uart_num, U0TXD_GPIO_NUM, U0RXD_GPIO_NUM, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // 特别注意下面两行，当前esp-idf版本有BUG必须，否则没有UART_WAKEUP事件
    gpio_sleep_set_direction(U0RXD_GPIO_NUM, GPIO_MODE_INPUT);
    gpio_sleep_set_pull_mode(U0RXD_GPIO_NUM, GPIO_PULLUP_ONLY);

    uart_set_wakeup_threshold(uart_num, 3);
    esp_err_t err = esp_sleep_enable_uart_wakeup(uart_num);
    if (err == ESP_OK)
        TY_LOGI("UART wakeup configured successfully\n");
    else
        TY_LOGE("ERROR: Failed to UART wakeup configure: %s (0x%x)\n", esp_err_to_name(err), err);
}

    // ========================
    // 1. 配置并启用动态功耗管理 (DFS)
    // ========================
void dfs_power_management_init(int max_mhz, int min_mhz, bool light_sleep)
{
    esp_err_t err;
#if ESP_IDF_VERSION < ESP_IDF_VERSION_VAL(5, 0, 0)
    #if CONFIG_IDF_TARGET_ESP32C3
        esp_pm_config_esp32c3_t pm_config = {
            .max_freq_mhz = max_mhz,
            .min_freq_mhz = min_mhz,
            .light_sleep_enable = light_sleep};
    #elif CONFIG_IDF_TARGET_ESP32S3
        esp_pm_config_esp32s3_t pm_config = {
            .max_freq_mhz = max_mhz,
            .min_freq_mhz = min_mhz,
            .light_sleep_enable = light_sleep};
    #elif CONFIG_IDF_TARGET_ESP32
        esp_pm_config_esp32_t pm_config = {
            .max_freq_mhz = max_mhz,
            .min_freq_mhz = min_mhz,
            .light_sleep_enable = light_sleep};
    #elif CONFIG_IDF_TARGET_ESP32S2
        esp_pm_config_esp32s2_t pm_config = {
            .max_freq_mhz = max_mhz,
            .min_freq_mhz = min_mhz,
            .light_sleep_enable = light_sleep};
    #endif
#else
    esp_pm_config_t pm_config = {
        .max_freq_mhz = max_mhz,
        .min_freq_mhz = min_mhz,
        .light_sleep_enable = light_sleep
    };
#endif
    err = esp_pm_configure(&pm_config);
    if (err == ESP_OK)
        TY_LOGI("Power management configured successfully\n");
    else
        TY_LOGE("ERROR: Failed to configure power management: %s (0x%x)\n", esp_err_to_name(err), err);

    err = esp_pm_lock_create(ESP_PM_NO_LIGHT_SLEEP, 0, "dfs_lock", &dfs_lock);    // 创建DFS锁
    if (err == ESP_OK)
        TY_LOGI("DFS Lock configured successfully\n");
    else
        TY_LOGE("ERROR: Failed to configure DFS Lock: %s (0x%x)\n", esp_err_to_name(err), err);
}