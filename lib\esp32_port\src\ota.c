#include "esp_err.h"
#include "esp_ota_ops.h"
#include "esp_partition.h"
#include "esp_app_format.h"
#include "esp_system.h"
#include "tuya_ota.h"
#include "tuya_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

tuya_ota_handle_t ota_handle;
extern tuya_iot_client_t client;

// ESP32 OTA上下文结构
typedef struct {
    esp_ota_handle_t ota_handle;
    const esp_partition_t* ota_partition;
    bool ota_started;
    size_t total_written;
} esp32_ota_context_t;

static esp32_ota_context_t esp32_ota_ctx = {0};

void user_ota_event_cb(tuya_ota_handle_t *handle, tuya_ota_event_t *event)
{
    esp_err_t ret = ESP_OK;

    switch (event->id)
    {
    case TUYA_OTA_EVENT_START:
        TY_LOGI("Tuya MQTT OTA start, file size: %zu bytes", event->file_size);

        // 重置OTA上下文
        memset(&esp32_ota_ctx, 0, sizeof(esp32_ota_ctx));

        // 获取下一个OTA分区
        esp32_ota_ctx.ota_partition = esp_ota_get_next_update_partition(NULL);
        if (esp32_ota_ctx.ota_partition == NULL) {
            TY_LOGE("Failed to get OTA partition");
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        TY_LOGI("OTA partition: %s, size: %zu bytes",
               esp32_ota_ctx.ota_partition->label, esp32_ota_ctx.ota_partition->size);

        // 检查文件大小是否超过分区大小
        if (event->file_size > esp32_ota_ctx.ota_partition->size) {
            TY_LOGE("File size (%zu) exceeds OTA partition size (%zu)",
                   event->file_size, esp32_ota_ctx.ota_partition->size);
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        // 开始OTA
        ret = esp_ota_begin(esp32_ota_ctx.ota_partition, event->file_size, &esp32_ota_ctx.ota_handle);
        if (ret != ESP_OK) {
            TY_LOGE("esp_ota_begin failed: %s", esp_err_to_name(ret));
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        esp32_ota_ctx.ota_started = true;
        TY_LOGI("ESP32 OTA started successfully");
        break;

    case TUYA_OTA_EVENT_ON_DATA:
        if (!esp32_ota_ctx.ota_started) {
            TY_LOGE("OTA not started, ignoring data");
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        TY_LOGD("OTA data: %d bytes, offset: %zu/%zu",
               event->data_len, event->offset, event->file_size);

        // 写入固件数据
        ret = esp_ota_write(esp32_ota_ctx.ota_handle, event->data, event->data_len);
        if (ret != ESP_OK) {
            TY_LOGE("esp_ota_write failed: %s", esp_err_to_name(ret));
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        esp32_ota_ctx.total_written += event->data_len;
        TY_LOGD("Written %zu/%zu bytes", esp32_ota_ctx.total_written, event->file_size);
        break;

    case TUYA_OTA_EVENT_FINISH:
        TY_LOGI("Tuya MQTT OTA finish, total written: %zu bytes", esp32_ota_ctx.total_written);

        if (!esp32_ota_ctx.ota_started) {
            TY_LOGE("OTA not started, cannot finish");
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        // 完成OTA写入
        ret = esp_ota_end(esp32_ota_ctx.ota_handle);
        if (ret != ESP_OK) {
            TY_LOGE("esp_ota_end failed: %s", esp_err_to_name(ret));
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        // 设置启动分区
        ret = esp_ota_set_boot_partition(esp32_ota_ctx.ota_partition);
        if (ret != ESP_OK) {
            TY_LOGE("esp_ota_set_boot_partition failed: %s", esp_err_to_name(ret));
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
            return;
        }

        TY_LOGI("Tuya MQTT OTA completed successfully!");
        TY_LOGI("New firmware will be applied after restart");

        // 报告升级完成状态
        tuya_ota_upgrade_status_report(handle, TUS_UPGRD_FINI);

        // 延迟重启以确保状态报告发送成功
        TY_LOGI("Restarting in 3 seconds to apply new firmware...");
        vTaskDelay(pdMS_TO_TICKS(3000));
        esp_restart();
        break;

    case TUYA_OTA_EVENT_FAULT:
        TY_LOGE("Tuya MQTT OTA fault");

        // 如果OTA已经开始，需要中止
        if (esp32_ota_ctx.ota_started) {
            esp_ota_abort(esp32_ota_ctx.ota_handle);
            esp32_ota_ctx.ota_started = false;
            TY_LOGI("OTA aborted due to fault");
        }

        tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
        break;

    default:
        TY_LOGW("Unknown OTA event: %d", event->id);
        break;
    }
}

