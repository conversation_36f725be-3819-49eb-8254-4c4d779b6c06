#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "system_interface.h"
#if CONFIG_IDF_TARGET_ESP32 || CONFIG_IDF_TARGET_ESP32S3 || CONFIG_IDF_TARGET_ESP32S2
    #include "esp_psram.h"
    static inline bool psram_is_initialized() { return esp_psram_is_initialized(); }
#else
    static inline bool psram_is_initialized() { return false; }
#endif

/*
 * Time conversion constants.
 */
#define NANOSECONDS_PER_MILLISECOND    ( 1000000L )    /**< @brief Nanoseconds per millisecond. */
#define MILLISECONDS_PER_SECOND        ( 1000L )       /**< @brief Milliseconds per second. */


void* system_malloc(size_t n)
{
    if (psram_is_initialized() && n >= 4096)
        return heap_caps_malloc(n, MALLOC_CAP_SPIRAM|MALLOC_CAP_8BIT);
    else
        return heap_caps_malloc(n, MALLOC_CAP_INTERNAL|MALLOC_CAP_8BIT);
}

void* system_calloc(size_t n, size_t size)
{
    if (psram_is_initialized() && n*size >= 4096)
        return heap_caps_calloc(n, size, MALLOC_CAP_SPIRAM|MALLOC_CAP_8BIT);
    else
        return heap_caps_calloc(n, size, MALLOC_CAP_INTERNAL|MALLOC_CAP_8BIT);
}

void system_free(void *ptr)
{
    heap_caps_free(ptr);
}

uint32_t system_ticks( void )
{
    TickType_t ticks = xTaskGetTickCount() * portTICK_PERIOD_MS;
    return ( uint32_t ) ticks;
}

uint32_t system_timestamp()
{
    time_t now;
    // struct tm timeinfo;
    time(&now);
    // localtime_r(&now, &timeinfo);
    return (uint32_t)now;
}

uint32_t system_random(void)
{
    return (uint32_t)(0xffffffff & rand());
}

#ifdef __cplusplus
}
#endif
